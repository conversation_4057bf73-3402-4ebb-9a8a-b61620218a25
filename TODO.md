fix markdown display like acli rovodev
ensure newlines after and before headings

is there any way to display 'ctrl+c to interrupt' in gray, while AI is thinking below the prompt, AND also remove it
once AI has generated
response?

fix broken tool calling, it still does not work, for instance response body includes this, but it is not recognized as tool call or not ran? 

"tool_calls": [
{
"function": {
"arguments": "{\"pattern\":\"/clear\",\"path\":\"/Users/<USER>/Developer/ai/rust-ai-agent\"}",
"call_id": null,
"name": "search_code"
},
"id": "call_18418396",
"type": "function"
}
]


while in this, ctrl-c or ctrl-d do also not work yet
API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 200ms...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 400ms...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 800ms...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 1.6s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 3.2s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 6.4s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 12.8s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 25.6s...

use a global boolean for that, set in handler.rs depending on if AI is thinking or not
ctrl-c while AI is thinking should abort the AI response
then check that in suspendable_editor.rs and somehow send an event back to abort the network request

use transient prompt to make prompt disappear, check example:
cargo run --example transient_prompt

inject workspace files into system prompt

needs runtime model change capabilities
add gemini 2.0 flash provider ((has tools))
add cohere provider (has tools)
