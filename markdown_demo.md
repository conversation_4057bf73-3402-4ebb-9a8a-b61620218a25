# Markdown Formatting Demonstration

## Headings

Markdown supports multiple heading levels:

# Heading 1
## Heading 2
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6

## Lists

### Unordered Lists
- Item 1
- Item 2
  - Nested item 2.1
  - Nested item 2.2
- Item 3

### Ordered Lists
1. First item
2. Second item
   1. Nested item 2.1
   2. Nested item 2.2
3. Third item

## Text Formatting

**Bold text** and *italic text* are supported.
***Bold and italic*** can be combined.
~~Strikethrough~~ is also available.

## Code

Inline `code` can be displayed.

```rust
fn main() {
    println!("Hello, world!");
    let x = 42;
    if x > 0 {
        println!("Positive number");
    }
}
```

## Blockquotes

> This is a blockquote
> 
> It can span multiple lines
>
> > And can be nested

## Tables

| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Links

[Link to GitHub](https://github.com)

## Horizontal Rule

---