The PollinationsAI API reports `grok-3-mini-high` back.

# Features

## Tool Support

The AI agent now supports proper OpenAI-compatible tool definitions sent in the API request body with **automatic tool
chaining**. The AI can automatically call multiple tools in sequence without user intervention.

### ✅ **Enhanced Tool Display**

- **Compact tool calls**: `└── Calling tool_name: {"arg": "value"...` (truncated at 200 chars)
- **Smart output truncation**: Tool outputs are limited to ~15 lines, truncating in the middle for longer outputs
- **Automatic continuation**: AI seamlessly chains tools based on results

### ✅ **Automatic Tool Chaining**

The AI can automatically follow up on tool results by calling additional tools, just like in advanced AI assistants. For
example:

1. AI calls `search_files` to find relevant files
2. AI automatically calls `read_file` to examine found files
3. AI calls `edit_block` to make targeted changes
4. AI calls `get_file_info` to verify the changes

## File System Tools

#### `read_file` ⚡ *Automatic*

Read contents from local filesystem with line-based pagination.

**Parameters:**

- `path` (string, required): The full path to the file to read
- `offset` (number, optional): Line offset to start reading from (positive from start, negative from end)
- `length` (number, optional): Number of lines to read

#### `read_multiple_files` ⚡ *Automatic*

Read multiple files simultaneously.

**Parameters:**

- `paths` (array, required): Array of file paths to read

#### `write_file` ⚡ *Automatic*

Write file contents with options for rewrite or append mode.

**Parameters:**

- `path` (string, required): The full path to the file to write
- `content` (string, required): The content to write into the file
- `mode` (string, optional): Write mode: 'rewrite' (default) or 'append'

#### `create_directory` ⚡ *Automatic*

Create a new directory or ensure it exists.

**Parameters:**

- `path` (string, required): The path of the directory to create

#### `list_directory` ⚡ *Automatic*

Get detailed listing of files and directories with metadata.

**Parameters:**

- `path` (string, required): The path to the directory
- `recursive` (boolean, optional): Whether to list recursively. Defaults to false

#### `move_file` ⚡ *Automatic*

Move or rename files and directories.

**Parameters:**

- `source` (string, required): The source path to move from
- `destination` (string, required): The destination path to move to

#### `get_file_info` ⚡ *Automatic*

Retrieve detailed metadata about a file or directory.

**Parameters:**

- `path` (string, required): The path to get information about

## Search Tools

#### `search_files` ⚡ *Automatic*

Find files by name using case-insensitive substring matching.

**Parameters:**

- `pattern` (string, required): The filename pattern to search for
- `path` (string, optional): The directory to search in (defaults to current directory)

#### `search_code` ⚡ *Automatic*

Search for text/code patterns within file contents using built-in search.

**Parameters:**

- `pattern` (string, required): The text pattern to search for
- `path` (string, optional): The directory to search in (defaults to current directory)
- `file_pattern` (string, optional): File pattern to limit search (e.g., '*.rs', '*.py')

## Text Editing

#### `edit_block` ⚡ *Automatic*

Apply targeted text replacements with enhanced prompting for smaller edits.

**Parameters:**

- `path` (string, required): The file path to edit
- `old_text` (string, required): The exact text to replace
- `new_text` (string, required): The new text to replace with

## System Tools

#### `execute_command` 🔒 *Requires Confirmation*

Execute a terminal command with configurable timeout and user confirmation.

**Parameters:**

- `command` (string, required): The command to execute (e.g., "git status", "cargo build")
- `cwd` (string, optional): The current working directory for the command. Defaults to the TUI's current directory
- `timeout_seconds` (number, optional): Timeout in seconds for command execution. Defaults to 30 seconds

### Key Features:

- ✅ **10 comprehensive tools** covering file operations, search, and editing
- ✅ **Proper OpenAI tool format** in API requests
- ✅ **Automatic tool chaining** - AI can call multiple tools in sequence
- ✅ **Smart output truncation** - keeps terminal clean with 15-line limit
- ✅ **Compact tool display** - shows tool calls in `└── Calling tool: {...` format
- ✅ **No user intervention needed** for file operations (read, write, list, search, edit)
- ✅ **User confirmation required** only for potentially dangerous operations (execute_command)
- ✅ **Line-based pagination** for reading large files
- ✅ **Detailed metadata** for file and directory information
- ✅ **Configurable timeout** support for command execution
- ✅ **Async execution** with timeout handling
- ✅ **Working directory** support
- ✅ **Comprehensive error handling** for all operations