[package]
name = "rust_llm_tui"
version = "0.1.0"
edition = "2021"

[dependencies]
textwrap = { version = "0.16", features = ["terminal_size"] }
# crossterm and reedline versions need to be compatible
crossterm = { version = "0.27.0", features = ["event-stream"] }
reedline = "0.32.0"

tokio = { version = "1.35.1", features = ["full"] }
reqwest = { version = "0.12.20", features = ["json", "rustls-tls"], default-features = false }
serde = { version = "1.0.195", features = ["derive"] }
serde_json = "1.0.111"
syntect = "5.2.0"
lazy_static = "1.4.0"
pulldown-cmark = "0.13.0"
thiserror = "2.0.12"
clap = { version = "4.5.4", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }
tiktoken-rs = "0.7.0"
console = "0.15.8"
nu-ansi-term = "0.50.1"
