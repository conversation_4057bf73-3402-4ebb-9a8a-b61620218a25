pub mod api;
pub mod tools;
pub mod types;
pub use api::call_llm_api;
pub use types::{ChatMessage, ToolCallAction};

pub const SYSTEM_PROMPT: &str = include_str!("../../prompts/system_prompt.txt");

// Function to attempt parsing a string as a ToolCallAction
pub fn try_parse_tool_call(content: &str) -> Option<ToolCallAction> {
    let trimmed_content = content.trim();
    if trimmed_content.starts_with('{') && trimmed_content.ends_with('}') {
        serde_json::from_str::<ToolCallAction>(trimmed_content).ok()
    } else {
        None
    }
}
