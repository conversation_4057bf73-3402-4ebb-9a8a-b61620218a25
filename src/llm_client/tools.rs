use super::types::{Tool, <PERSON><PERSON><PERSON>unction, ToolParameter, ToolParameters};
use std::collections::HashMap;

// Function to create tool definitions
pub fn create_tools() -> Vec<Tool> {
    vec![
        create_read_file_tool(),
        create_read_multiple_files_tool(),
        create_write_file_tool(),
        create_create_directory_tool(),
        create_list_directory_tool(),
        create_move_file_tool(),
        create_search_files_tool(),
        create_search_code_tool(),
        create_get_file_info_tool(),
        create_edit_block_tool(),
        create_execute_command_tool(),
    ]
}

fn create_read_file_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "read_file".to_string(),
            description: "Read contents from local filesystem or URLs with line-based pagination (supports positive/negative offset and length parameters)".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The full path to the file to read".to_string(),
                        },
                    );
                    props.insert(
                        "offset".to_string(),
                        ToolParameter {
                            param_type: "number".to_string(),
                            description: "Line offset to start reading from (positive from start, negative from end)".to_string(),
                        },
                    );
                    props.insert(
                        "length".to_string(),
                        ToolParameter {
                            param_type: "number".to_string(),
                            description: "Number of lines to read".to_string(),
                        },
                    );
                    props
                },
                required: vec!["path".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_read_multiple_files_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "read_multiple_files".to_string(),
            description: "Read multiple files simultaneously".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "paths".to_string(),
                        ToolParameter {
                            param_type: "array".to_string(),
                            description: "Array of file paths to read".to_string(),
                        },
                    );
                    props
                },
                required: vec!["paths".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_write_file_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "write_file".to_string(),
            description: "Write file contents with options for rewrite or append mode (uses configurable line limits)".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The full path to the file to write".to_string(),
                        },
                    );
                    props.insert(
                        "content".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The content to write into the file".to_string(),
                        },
                    );
                    props.insert(
                        "mode".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "Write mode: 'rewrite' (default) or 'append'".to_string(),
                        },
                    );
                    props
                },
                required: vec!["path".to_string(), "content".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_create_directory_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "create_directory".to_string(),
            description: "Create a new directory or ensure it exists".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The path of the directory to create".to_string(),
                        },
                    );
                    props
                },
                required: vec!["path".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_list_directory_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "list_directory".to_string(),
            description: "Get detailed listing of files and directories".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The path to the directory".to_string(),
                        },
                    );
                    props.insert(
                        "recursive".to_string(),
                        ToolParameter {
                            param_type: "boolean".to_string(),
                            description: "Whether to list recursively. Defaults to false"
                                .to_string(),
                        },
                    );
                    props
                },
                required: vec!["path".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_move_file_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "move_file".to_string(),
            description: "Move or rename files and directories".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "source".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The source path to move from".to_string(),
                        },
                    );
                    props.insert(
                        "destination".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The destination path to move to".to_string(),
                        },
                    );
                    props
                },
                required: vec!["source".to_string(), "destination".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_search_files_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "search_files".to_string(),
            description: "Find files by name using case-insensitive substring matching".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "pattern".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The filename pattern to search for".to_string(),
                        },
                    );
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description:
                                "The directory to search in (defaults to current directory)"
                                    .to_string(),
                        },
                    );
                    props
                },
                required: vec!["pattern".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_search_code_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "search_code".to_string(),
            description: "Search for text/code patterns within file contents using ripgrep"
                .to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "pattern".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The text pattern to search for".to_string(),
                        },
                    );
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description:
                                "The directory to search in (defaults to current directory)"
                                    .to_string(),
                        },
                    );
                    props.insert(
                        "file_pattern".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "File pattern to limit search (e.g., '*.rs', '*.py')"
                                .to_string(),
                        },
                    );
                    props
                },
                required: vec!["pattern".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_get_file_info_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "get_file_info".to_string(),
            description: "Retrieve detailed metadata about a file or directory".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The path to get information about".to_string(),
                        },
                    );
                    props
                },
                required: vec!["path".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_edit_block_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "edit_block".to_string(),
            description: "Apply targeted text replacements with enhanced prompting for smaller edits (includes character-level diff feedback)".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "path".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The file path to edit".to_string(),
                        },
                    );
                    props.insert(
                        "old_text".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The exact text to replace".to_string(),
                        },
                    );
                    props.insert(
                        "new_text".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The new text to replace with".to_string(),
                        },
                    );
                    props
                },
                required: vec!["path".to_string(), "old_text".to_string(), "new_text".to_string()],
                additional_properties: false,
            },
        },
    }
}

fn create_execute_command_tool() -> Tool {
    Tool {
        tool_type: "function".to_string(),
        function: ToolFunction {
            name: "execute_command".to_string(),
            description: "Execute a terminal command with configurable timeout. User confirmation will be sought before execution".to_string(),
            parameters: ToolParameters {
                param_type: "object".to_string(),
                properties: {
                    let mut props = HashMap::new();
                    props.insert(
                        "command".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The command to execute (e.g., 'git status', 'cargo build')".to_string(),
                        },
                    );
                    props.insert(
                        "cwd".to_string(),
                        ToolParameter {
                            param_type: "string".to_string(),
                            description: "The current working directory for the command. Defaults to the TUI's current directory.".to_string(),
                        },
                    );
                    props.insert(
                        "timeout_seconds".to_string(),
                        ToolParameter {
                            param_type: "number".to_string(),
                            description: "Timeout in seconds for command execution. Defaults to 30 seconds.".to_string(),
                        },
                    );
                    props
                },
                required: vec!["command".to_string()],
                additional_properties: false,
            },
        },
    }
}
