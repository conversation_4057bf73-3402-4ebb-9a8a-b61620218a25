use super::tools::create_tools;
use super::types::{ChatCompletionRequest, ChatCompletionResponse, ChatMessage, LLMError};
use reqwest::Client;
use std::time::Duration;
use tokio::time::sleep;

const POLLINATIONS_API_URL: &str = "https://text.pollinations.ai/openai/v1/chat/completions";
const MODEL_NAME: &str = "grok";

// Helper function to make a single API call attempt
async fn make_api_call_attempt(
    client: &Client,
    request_payload: &ChatCompletionRequest,
) -> Result<String, LLMError> {
    let response = client
        .post(POLLINATIONS_API_URL)
        .json(request_payload)
        .send()
        .await?;

    if response.status().is_success() {
        let chat_response = response.json::<ChatCompletionResponse>().await?;
        if let Some(choice) = chat_response.choices.first() {
            Ok(choice.message.content.clone())
        } else {
            Err(LLMError::Parse("No choices in response".to_string()))
        }
    } else {
        let status = response.status();
        let error_body = response
            .text()
            .await
            .unwrap_or_else(|_| "Could not retrieve error body".to_string());
        if error_body.trim().is_empty() {
            return Err(LLMError::ApiError(format!(
                "Status {}: No error body provided",
                status
            )));
        }
        Err(LLMError::ApiError(format!(
            "Status {}: {}",
            status, error_body
        )))
    }
}

pub async fn call_llm_api(
    client: &Client,
    messages_for_api_call: Vec<ChatMessage>,
) -> Result<String, LLMError> {
    let tools = create_tools();
    let request_payload = ChatCompletionRequest {
        model: MODEL_NAME.to_string(),
        messages: messages_for_api_call,
        stream: false,
        tools: Some(tools),
    };

    let retry_delays = [
        Duration::from_millis(200),
        Duration::from_millis(400),
        Duration::from_millis(800),
        Duration::from_millis(1600),
        Duration::from_millis(3200),
        Duration::from_millis(6400),
        Duration::from_millis(12800),
        Duration::from_millis(25600),
        Duration::from_millis(51200),
    ];
    let max_retries = retry_delays.len();

    // First attempt
    let mut result = make_api_call_attempt(client, &request_payload).await;

    // Retry loop
    let mut attempt = 0;
    while result.is_err() && attempt < max_retries {
        let delay = retry_delays[attempt];
        sleep(delay).await;
        attempt += 1;
        result = make_api_call_attempt(client, &request_payload).await;
    }

    // If we've exhausted all retries and still have an error, add context to the error
    if result.is_err() && attempt > 0 {
        let err = result.unwrap_err();
        return Err(LLMError::ApiError(format!(
            "{} (after {} retries)",
            err, attempt
        )));
    }

    result
}
