use crate::llm_client::ChatMessage;
use chrono::Local;
use serde::{Deserialize, Serialize};
use std::{
    fs::{self, File},
    io::{self, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>uf<PERSON>riter, Error<PERSON><PERSON>},
    path::{Path, PathBuf},
};

const SESSION_DIR_NAME: &str = "chat_sessions";

#[derive(Serialize, Deserialize, Debug)]
struct SessionData {
    messages: Vec<ChatMessage>,
}

pub fn ensure_session_dir_exists() -> io::Result<PathBuf> {
    let path = PathBuf::from(SESSION_DIR_NAME);
    if !path.exists() {
        fs::create_dir_all(&path)?;
    }
    Ok(path)
}

pub fn generate_new_session_id() -> String {
    Local::now().format("session_%Y%m%d_%H%M%S").to_string()
}

pub fn get_session_file_path(session_dir: &Path, session_id: &str) -> PathBuf {
    session_dir.join(format!("{}.json", session_id))
}

pub fn save_session(session_file_path: &Path, history: &[ChatMessage]) -> io::Result<()> {
    let file = File::create(session_file_path)?;
    let writer = BufWriter::new(file);
    let data = SessionData {
        messages: history.to_vec(),
    };
    serde_json::to_writer_pretty(writer, &data).map_err(io::Error::other)
}

pub fn load_session(session_file_path: &Path) -> io::Result<Vec<ChatMessage>> {
    if !session_file_path.exists() {
        return Err(io::Error::new(
            ErrorKind::NotFound,
            format!("Session file not found: {:?}", session_file_path),
        ));
    }
    let file = File::open(session_file_path)?;
    // Handle empty file case
    if file.metadata()?.len() == 0 {
        return Ok(Vec::new());
    }
    let reader = BufReader::new(file);
    match serde_json::from_reader(reader) {
        Ok(data @ SessionData { .. }) => Ok(data.messages),
        Err(e) => {
            if e.is_eof() {
                // Should be caught by empty file check, but good fallback
                Ok(Vec::new())
            } else {
                Err(io::Error::new(ErrorKind::InvalidData, e))
            }
        }
    }
}
