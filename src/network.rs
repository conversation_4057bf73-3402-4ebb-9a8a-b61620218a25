use crate::cli::Cli;
use reqwest::Client;
use std::time::Duration;

pub fn create_client(timeout: Duration, args: &Cli) -> Client {
    let mut builder = Client::builder().timeout(timeout);
    if let Some(port) = args.proxy_localhost {
        let proxy_url = format!("http://localhost:{}", port);
        eprintln!("Using proxy: {}", proxy_url);
        match reqwest::Proxy::all(&proxy_url) {
            Ok(proxy) => {
                builder = builder.proxy(proxy).danger_accept_invalid_certs(true);
            }
            Err(e) => {
                eprintln!(
                    "Error setting up proxy '{}': {}. Proceeding without proxy.",
                    proxy_url, e
                );
            }
        }
    }
    builder
        .build()
        .map_err(|e| std::io::Error::other(e.to_string()))
        .expect("Failed to build HTTP client")
}
