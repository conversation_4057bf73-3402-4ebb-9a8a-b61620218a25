use crate::llm_client::{try_parse_tool_call, ChatMessage, ToolCallAction};
use pulldown_cmark::{CodeBlockKind, Event, Options, Parser, Tag, TagEnd};

#[derive(Clone, Debug, PartialEq)]
pub enum MessageContent {
    Text(String),
    FormattedText(Vec<FormattedTextElement>),
    CodeBlock {
        language: Option<String>,
        content: String,
    },
    ToolCall(ToolCallAction),
}

#[derive(Clone, Debug, PartialEq)]
pub enum FormattedTextElement {
    Text(String),
    Heading { level: u8, text: String },
    BoldOrItalic(String),
    Strikethrough(String),
    InlineCode(String),
    ListItem { indent_level: u8, text: String },
    LineBreak,
    HorizontalRuler,
}

#[derive(Clone, Debug)]
pub struct AppMessage {
    pub sender: String,
    pub parts: Vec<MessageContent>,
}

pub struct App {
    #[allow(dead_code)]
    pub http_client: reqwest::Client, // Used in handler.rs but passed separately
    pub messages: Vec<AppMessage>, // For display structuring and logging
    pub conversation_history_for_llm: Vec<ChatMessage>, // For actual LLM context
    pub should_quit: bool,
}

impl App {
    pub fn new(http_client: reqwest::Client) -> Self {
        App {
            http_client,
            messages: Vec::new(),
            conversation_history_for_llm: Vec::new(),
            should_quit: false,
        }
    }

    pub fn add_user_message_to_llm_history(&mut self, content: String) {
        self.conversation_history_for_llm.push(ChatMessage {
            role: "user".to_string(),
            content,
        });
    }

    pub fn add_assistant_response_to_llm_history(&mut self, raw_content: String) {
        // If the raw_content is a JSON representation of a tool call, store that.
        // Otherwise, store the raw text/markdown.
        // This aligns with OpenAI: assistant message can have `content` or `tool_calls`.
        // We simplify by putting the tool_call JSON string *into* content if it's a tool call.
        let content_for_llm = if let Some(tool_call) = try_parse_tool_call(&raw_content) {
            // Store the JSON string of the tool call action
            serde_json::to_string(&tool_call).unwrap_or_else(|_| raw_content.clone())
        } else {
            raw_content
        };

        self.conversation_history_for_llm.push(ChatMessage {
            role: "assistant".to_string(),
            content: content_for_llm,
        });
    }

    pub fn add_tool_response_to_llm_history(&mut self, _tool_name: &str, response_content: String) {
        // _tool_name is not used if ChatMessage doesn't have a 'name' field for 'tool' role.
        // The LLM knows which tool was called from the preceding assistant message.
        self.conversation_history_for_llm.push(ChatMessage {
            role: "tool".to_string(),  // Standard OpenAI role for tool results
            content: response_content, // The textual output from the tool
        });
    }

    pub fn create_ai_app_message_from_raw(&self, raw_content: &str) -> AppMessage {
        // This function parses the AI's raw response string into structured AppMessage for display.
        // It checks if the response is a tool call or regular text/markdown.
        if let Some(tool_call) = try_parse_tool_call(raw_content) {
            AppMessage {
                sender: "AI".to_string(),
                parts: vec![MessageContent::ToolCall(tool_call.clone())],
            }
        } else {
            // Always parse markdown to get proper formatting
            // Only fall back to plain text if parsing produces no meaningful structure

            // Parse markdown with proper formatting support
            let mut parts = Vec::new();
            let mut formatted_elements = Vec::new();
            let parser = Parser::new_ext(raw_content, Options::ENABLE_STRIKETHROUGH);
            let mut in_code_block_lang: Option<String> = None;
            let mut in_heading_level: Option<u8> = None;
            let mut in_strong_or_italic = false;
            let mut in_strikethrough = false;

            let mut in_list_item = false;
            let mut list_depth: u8 = 0;

            for event in parser {
                match event {
                    Event::Start(Tag::CodeBlock(CodeBlockKind::Fenced(lang))) => {
                        // Flush any accumulated formatted elements
                        if !formatted_elements.is_empty() {
                            parts.push(MessageContent::FormattedText(formatted_elements.clone()));
                            formatted_elements.clear();
                        }

                        let lang_str = lang.into_string();
                        in_code_block_lang = Some(lang_str.clone());
                        parts.push(MessageContent::CodeBlock {
                            language: Some(lang_str),
                            content: String::new(),
                        });
                    }
                    Event::End(TagEnd::CodeBlock) => {
                        in_code_block_lang = None;
                    }
                    Event::Start(Tag::Heading { level, .. }) => {
                        in_heading_level = Some(level as u8);
                    }
                    Event::End(TagEnd::Heading { .. }) => {
                        in_heading_level = None;
                    }
                    Event::Start(Tag::Strikethrough) => {
                        in_strikethrough = true;
                    }
                    Event::End(TagEnd::Strikethrough) => {
                        in_strikethrough = false;
                    }
                    Event::Start(Tag::Emphasis) => {
                        in_strong_or_italic = true;
                    }
                    Event::End(TagEnd::Emphasis) => {
                        in_strong_or_italic = false;
                    }
                    Event::Start(Tag::Strong) => {
                        in_strong_or_italic = true;
                    }
                    Event::End(TagEnd::Strong) => {
                        in_strong_or_italic = false;
                    }
                    Event::Code(code_text) => {
                        // Inline code is a single event in pulldown-cmark
                        formatted_elements
                            .push(FormattedTextElement::InlineCode(code_text.to_string()));
                    }
                    Event::Start(Tag::Item) => {
                        in_list_item = true;
                    }
                    Event::End(TagEnd::Item) => {
                        in_list_item = false;
                    }
                    Event::Start(Tag::List(_)) => {
                        list_depth += 1;
                    }
                    Event::End(TagEnd::List(_)) => {
                        list_depth = list_depth.saturating_sub(1);
                    }
                    Event::Text(text_cow) => {
                        if in_code_block_lang.is_some() {
                            // Handle code block content
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push_str(&text_cow);
                            }
                        } else if let Some(level) = in_heading_level {
                            // Handle heading text
                            formatted_elements.push(FormattedTextElement::Heading {
                                level,
                                text: text_cow.to_string(),
                            });
                        } else if in_strong_or_italic {
                            formatted_elements
                                .push(FormattedTextElement::BoldOrItalic(text_cow.to_string()));
                        } else if in_strikethrough {
                            formatted_elements
                                .push(FormattedTextElement::Strikethrough(text_cow.to_string()));
                        } else if in_list_item {
                            // Handle list item text
                            formatted_elements.push(FormattedTextElement::ListItem {
                                indent_level: list_depth.saturating_sub(1),
                                text: text_cow.to_string(),
                            });
                        } else {
                            // Handle regular text
                            formatted_elements
                                .push(FormattedTextElement::Text(text_cow.to_string()));
                        }
                    }
                    Event::Rule => {
                        formatted_elements.push(FormattedTextElement::HorizontalRuler);
                    }
                    Event::HardBreak => {
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push('\n');
                            }
                        } else {
                            formatted_elements.push(FormattedTextElement::LineBreak);
                        }
                    }
                    Event::SoftBreak => {
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push('\n');
                            }
                        } else {
                            formatted_elements.push(FormattedTextElement::Text(" ".to_string()));
                        }
                    }
                    Event::Html(html_cow) => {
                        // Treat HTML as text
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push_str(&html_cow);
                            }
                        } else {
                            formatted_elements
                                .push(FormattedTextElement::Text(html_cow.to_string()));
                        }
                    }
                    _ => {}
                }
            }

            // Add any remaining formatted elements
            let has_formatted_elements = !formatted_elements.is_empty();
            if has_formatted_elements {
                parts.push(MessageContent::FormattedText(formatted_elements));
            }

            // Fallback if parsing results in no parts but raw_content is not empty
            if parts.is_empty() && !raw_content.is_empty() {
                // Fall back to plain text if no meaningful structure was found
                parts.push(MessageContent::Text(raw_content.to_string()));
            }

            AppMessage {
                sender: "AI".to_string(),
                parts,
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_markdown_processing() {
        let test_markdown = r#"### Test Heading

This is regular text with **bold content** and some `inline code` here.

## Another Heading
- First list item with **bold**
- Second list item with `code`"#;

        let app = App::new(reqwest::Client::new());
        let app_message = app.create_ai_app_message_from_raw(test_markdown);

        // Should have FormattedText content with proper markdown parsing
        assert_eq!(app_message.parts.len(), 1);
        match &app_message.parts[0] {
            MessageContent::FormattedText(elements) => {
                // Should have headings, text, bold, and inline code elements
                assert!(elements.len() > 5);

                // Verify we have the expected element types
                let has_heading = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::Heading { .. }));
                let has_bold = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::BoldOrItalic(_)));
                let has_inline_code = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::InlineCode(_)));
                let has_list_item = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::ListItem { .. }));

                assert!(has_heading, "Should have heading elements");
                assert!(has_bold, "Should have bold elements");
                assert!(has_inline_code, "Should have inline code elements");
                assert!(has_list_item, "Should have list item elements");
            }
            _ => panic!("Expected FormattedText content"),
        }
    }
}
