use lazy_static::lazy_static;
use std::io::Cursor;
use syntect::easy::HighlightLines;
use syntect::highlighting::{Theme, ThemeSet};
use syntect::parsing::SyntaxSet;
use syntect::util::{as_24_bit_terminal_escaped, LinesWithEndings};

/// The theme name to use for syntax highlighting
// pub const THEME_NAME: &str = "Dracula";
pub const THEME_NAME: &str = "OneHalfDark";

// Include the Dracula theme file directly in the binary
const DRACULA_THEME_BYTES: &[u8] = include_bytes!("../theme/DimaOneHalfDark.tmTheme");

lazy_static! {
    /// Syntax set with default definitions and newlines support
    pub static ref SYNTAX_SET: SyntaxSet = SyntaxSet::load_defaults_newlines();

    /// Theme set with default themes plus our custom Dracula theme
    pub static ref THEME_SET: ThemeSet = init_theme_set();
}

/// Initialize the theme set with default themes and add our custom Dracula theme
fn init_theme_set() -> ThemeSet {
    let mut ts = ThemeSet::load_defaults();

    // Load the Dracula theme from the included bytes
    match load_theme_from_bytes() {
        Ok(dracula_theme) => {
            ts.themes.insert(THEME_NAME.to_string(), dracula_theme);
        }
        Err(e) => {
            eprintln!("[Syntax Highlighting] Failed to load Dracula theme: {}", e);
        }
    }

    ts
}

/// Load the Dracula theme from the embedded bytes
fn load_theme_from_bytes() -> Result<Theme, Box<dyn std::error::Error>> {
    let mut cursor = Cursor::new(DRACULA_THEME_BYTES);
    Ok(ThemeSet::load_from_reader(&mut cursor)?)
}

/// Map language names to file extensions for syntax highlighting
fn map_language_to_extension(lang: &str) -> String {
    let lang_lowercase = lang.to_lowercase();
    let extension = match lang_lowercase.as_str() {
        "python" | "py3" | "python3" => "py",
        "rust" => "rs",
        "javascript" | "js" => "js",
        "typescript" | "ts" => "ts",
        "html" => "html",
        "css" => "css",
        "json" => "json",
        "yaml" | "yml" => "yaml",
        "toml" => "toml",
        "markdown" | "md" => "md",
        "shell" | "bash" | "sh" | "zsh" => "sh",
        "java" => "java",
        "c" => "c",
        "cpp" | "c++" => "cpp",
        "go" => "go",
        "php" => "php",
        "ruby" | "rb" => "rb",
        "swift" => "swift",
        "kotlin" | "kt" => "kt",
        "sql" => "sql",
        "xml" => "xml",
        other => other,
    };
    extension.to_string()
}

/// Highlight code with syntax highlighting and return ANSI-colored string
pub fn highlight_code_to_ansi_string(code: &str, language_hint: Option<&str>) -> String {
    let syntax = language_hint
        .map(map_language_to_extension)
        .and_then(|ext| SYNTAX_SET.find_syntax_by_extension(&ext))
        .unwrap_or_else(|| SYNTAX_SET.find_syntax_plain_text());
    let theme = THEME_SET.themes.get(THEME_NAME).unwrap();
    let mut highlighter = HighlightLines::new(syntax, theme);
    let mut output = String::new();
    for line_str in LinesWithEndings::from(code) {
        match highlighter.highlight_line(line_str, &SYNTAX_SET) {
            Ok(ranges) => {
                // false = don't use theme's background (we handle it separately)
                let escaped = as_24_bit_terminal_escaped(&ranges[..], false);
                output.push_str(&escaped);
            }
            Err(e) => {
                eprintln!(
                    "[Syntax Highlighting] Error highlighting line: {:?}. Line: '{}'",
                    e,
                    line_str.trim_end_matches('\n')
                );
                output.push_str(line_str);
            }
        }
    }

    // Ensure the output doesn't have an extra newline if the input didn't,
    // as our display logic will add one per line explicitly.
    if !code.ends_with('\n') && output.ends_with('\n') {
        output.pop();
        if output.ends_with('\r') {
            output.pop();
        }
    }

    output
}
