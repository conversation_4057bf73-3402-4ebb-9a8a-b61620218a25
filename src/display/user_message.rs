use crate::app::{AppMessage, MessageContent};
use console::measure_text_width;
use crossterm::style::{Print, Stylize};
use std::io::{self, stdout, Write}; // For measuring visible width of text with ANSI codes

pub fn print_user_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout(); // Use stdout for more control with crossterm
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24)); // Default width 80

    // Start with a clean approach
    let min_box_width = 8;
    let actual_cols = if terminal_width < min_box_width {
        min_box_width
    } else {
        terminal_width
    };

    // Draw the top border - this defines the exact width
    let top_border = format!("╭{}╮", "─".repeat((actual_cols - 2) as usize));
    crossterm::execute!(out, Print(format!("{}\n", top_border)))?;

    // Get the text content
    let text_content = if let Some(MessageContent::Text(text)) = app_msg.parts.first() {
        text.lines().next().unwrap_or("").trim()
    } else {
        ""
    };

    // Format the text with color
    let colored_text = format!("{}", text_content.cyan());
    let visible_text_width = measure_text_width(&colored_text);

    // Calculate the exact content width (excluding borders)
    let content_width = actual_cols as usize - 2; // -2 for left and right borders

    // Fixed parts of the middle line
    let left_part = "│ > "; // Left border and prompt
    let left_part_width = measure_text_width(left_part);

    // Calculate how much space is available for text and padding
    let available_for_text_and_padding = content_width - left_part_width;

    // Truncate text if needed
    let display_text = if visible_text_width > available_for_text_and_padding {
        // We need to truncate
        let mut truncated = String::new();
        let mut current_width = 0;

        for c in text_content.chars() {
            let char_width = measure_text_width(&c.to_string());
            if current_width + char_width > available_for_text_and_padding {
                break;
            }
            truncated.push(c);
            current_width += char_width;
        }

        truncated.cyan().to_string()
    } else {
        colored_text
    };

    // Recalculate the visible width after potential truncation
    let final_text_width = measure_text_width(&display_text);

    // Calculate padding to fill the remaining space exactly
    let padding_width = available_for_text_and_padding - final_text_width;
    let padding = " ".repeat(padding_width);

    // Construct and print the middle line
    let middle_line = format!("{}{}{} │", left_part, display_text, padding);
    crossterm::execute!(out, Print(format!("{}\n", middle_line)))?;

    // Draw the bottom border - exactly matching the top border width
    let bottom_border = format!("╰{}╯", "─".repeat((actual_cols - 2) as usize));
    crossterm::execute!(out, Print(format!("{}\n", bottom_border)))?;

    out.flush() // Ensure all buffered output is written
}
