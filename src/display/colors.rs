use crossterm::style::Color;

/// IntelliJ background color
pub const CODE_BLOCK_BACKGROUND: Color = Color::Rgb {
    r: 43,
    g: 43,
    b: 43,
};

/// Full white for difference to other text color
pub const BOLD_AND_ITALIC_BACKGROUND_COLOR: Color = Color::Rgb {
    r: 33,
    g: 33,
    b: 33,
};

/// Use same color as in Rovo Dev.
pub const INLINE_CODE_COLOR: Color = Color::Rgb {
    r: 108,
    g: 177,
    b: 205,
};

/// Use same color as in Rovo Dev.
pub const LIST_CIRCLE_COLOR: Color = Color::Rgb {
    r: 231,
    g: 185,
    b: 77,
};

/// Use same color as in Rovo Dev.
pub const HEADING_H4_COLOR: Color = Color::Rgb {
    r: 150,
    g: 150,
    b: 150,
};
