// Function to truncate tool output to ~15 lines, truncating in the middle if needed
pub fn truncate_tool_output(output: &str) -> String {
    let lines: Vec<&str> = output.lines().collect();

    if lines.len() <= 15 {
        // If 15 lines or fewer, return as-is
        output.to_string()
    } else {
        // Take first 7 lines, add truncation indicator, then last 7 lines
        let mut result = Vec::new();

        // First 7 lines
        for line in &lines[..7] {
            result.push(line.to_string());
        }

        // Truncation indicator
        result.push(format!("... ({} lines truncated) ...", lines.len() - 14));

        // Last 7 lines
        for line in &lines[lines.len() - 7..] {
            result.push(line.to_string());
        }

        result.join("\n")
    }
}
