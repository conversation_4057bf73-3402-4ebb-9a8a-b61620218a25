use std::{fs, time::SystemTime};

// Helper function to get file metadata
pub fn get_file_metadata(file_path: &str) -> String {
    match fs::metadata(file_path) {
        Ok(metadata) => {
            let file_type = if metadata.is_file() {
                "File"
            } else if metadata.is_dir() {
                "Directory"
            } else {
                "Other"
            };

            let size = metadata.len();
            let readonly = metadata.permissions().readonly();

            let created = metadata
                .created()
                .ok()
                .and_then(|time| time.duration_since(SystemTime::UNIX_EPOCH).ok())
                .map(|duration| {
                    let secs = duration.as_secs();
                    chrono::DateTime::from_timestamp(secs as i64, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_else(|| "Unknown".to_string())
                })
                .unwrap_or_else(|| "Unknown".to_string());

            let modified = metadata
                .modified()
                .ok()
                .and_then(|time| time.duration_since(SystemTime::UNIX_EPOCH).ok())
                .map(|duration| {
                    let secs = duration.as_secs();
                    chrono::DateTime::from_timestamp(secs as i64, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_else(|| "Unknown".to_string())
                })
                .unwrap_or_else(|| "Unknown".to_string());

            format!(
                "File information for '{}':\nType: {}\nSize: {} bytes\nReadonly: {}\nCreated: {}\nModified: {}",
                file_path, file_type, size, readonly, created, modified
            )
        }
        Err(e) => {
            format!("Failed to get metadata for '{}': {}", file_path, e)
        }
    }
}
