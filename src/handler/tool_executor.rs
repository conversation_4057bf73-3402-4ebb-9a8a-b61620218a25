use crate::{
    app::{App, AppMessage, MessageContent},
    display, llm_client,
};
use std::time::Duration;
use std::{
    fs,
    sync::{Arc, Mutex},
};
use tokio::process::Command as TokioCommand;
use tokio::time::timeout;

use super::{
    directory_tools::{list_directory_detailed, list_directory_detailed_recursive},
    edit_tools::edit_text_block,
    file_tools::{read_file_with_pagination, write_file_with_mode},
    metadata_tools::get_file_metadata,
    search_tools::{search_code_content, search_files_by_name},
};

pub async fn execute_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    match tool_call_action.tool_name.as_str() {
        "read_file" => {
            execute_read_file_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "read_multiple_files" => {
            execute_read_multiple_files_tool(tool_call_action, app_arc, ai_app_message_for_display)
                .await
        }
        "write_file" => {
            execute_write_file_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "create_directory" => {
            execute_create_directory_tool(tool_call_action, app_arc, ai_app_message_for_display)
                .await
        }
        "list_directory" => {
            execute_list_directory_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "move_file" => {
            execute_move_file_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "search_files" => {
            execute_search_files_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "search_code" => {
            execute_search_code_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "get_file_info" => {
            execute_get_file_info_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "edit_block" => {
            execute_edit_block_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        "execute_command" => {
            execute_command_tool(tool_call_action, app_arc, ai_app_message_for_display).await
        }
        _ => execute_unknown_tool(tool_call_action, app_arc, ai_app_message_for_display).await,
    }
}

async fn execute_read_file_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
    let offset = args.get("offset").and_then(|v| v.as_i64()).unwrap_or(0);
    let length = args.get("length").and_then(|v| v.as_i64());

    if let Some(file_path) = path_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = read_file_with_pagination(&file_path, offset, length);

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output)],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "read_file").await
    }
}

async fn execute_read_multiple_files_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let paths_opt = args.get("paths").and_then(|v| v.as_array());

    if let Some(paths_array) = paths_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let mut results = Vec::new();
        for path_value in paths_array {
            if let Some(path_str) = path_value.as_str() {
                let content = read_file_with_pagination(path_str, 0, None);
                results.push(format!("=== {} ===\n{}", path_str, content));
            }
        }
        let tool_output_str = results.join("\n\n");

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output)],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "read_multiple_files").await
    }
}

async fn execute_write_file_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
    let content_opt = args
        .get("content")
        .and_then(|v| v.as_str())
        .map(String::from);
    let mode = args
        .get("mode")
        .and_then(|v| v.as_str())
        .unwrap_or("rewrite");

    if let (Some(file_path), Some(content)) = (path_opt, content_opt) {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = write_file_with_mode(&file_path, &content, mode);

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "write_file").await
    }
}

async fn execute_create_directory_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);

    if let Some(dir_path) = path_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = match fs::create_dir_all(&dir_path) {
            Ok(_) => {
                format!("Directory '{}' created successfully.", dir_path)
            }
            Err(e) => {
                format!("Failed to create directory '{}': {}", dir_path, e)
            }
        };

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "create_directory").await
    }
}

async fn execute_list_directory_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
    let recursive = args
        .get("recursive")
        .and_then(|v| v.as_bool())
        .unwrap_or(false);

    if let Some(dir_path) = path_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = if recursive {
            list_directory_detailed_recursive(&dir_path)
        } else {
            list_directory_detailed(&dir_path)
        };

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "list_directory").await
    }
}

async fn execute_move_file_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let source_opt = args
        .get("source")
        .and_then(|v| v.as_str())
        .map(String::from);
    let dest_opt = args
        .get("destination")
        .and_then(|v| v.as_str())
        .map(String::from);

    if let (Some(source), Some(destination)) = (source_opt, dest_opt) {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = match fs::rename(&source, &destination) {
            Ok(_) => {
                format!("Successfully moved '{}' to '{}'", source, destination)
            }
            Err(e) => {
                format!("Failed to move '{}' to '{}': {}", source, destination, e)
            }
        };

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "move_file").await
    }
}

async fn execute_search_files_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let pattern_opt = args
        .get("pattern")
        .and_then(|v| v.as_str())
        .map(String::from);
    let path_opt = args
        .get("path")
        .and_then(|v| v.as_str())
        .map(String::from)
        .unwrap_or_else(|| ".".to_string());

    if let Some(pattern) = pattern_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = search_files_by_name(&pattern, &path_opt);

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "search_files").await
    }
}

async fn execute_search_code_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let pattern_opt = args
        .get("pattern")
        .and_then(|v| v.as_str())
        .map(String::from);
    let path_opt = args
        .get("path")
        .and_then(|v| v.as_str())
        .map(String::from)
        .unwrap_or_else(|| ".".to_string());
    let file_pattern_opt = args
        .get("file_pattern")
        .and_then(|v| v.as_str())
        .map(String::from);

    if let Some(pattern) = pattern_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = search_code_content(&pattern, &path_opt, file_pattern_opt.as_deref());

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "search_code").await
    }
}

async fn execute_get_file_info_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);

    if let Some(file_path) = path_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = get_file_metadata(&file_path);

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "get_file_info").await
    }
}

async fn execute_edit_block_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
    let old_text_opt = args
        .get("old_text")
        .and_then(|v| v.as_str())
        .map(String::from);
    let new_text_opt = args
        .get("new_text")
        .and_then(|v| v.as_str())
        .map(String::from);

    if let (Some(file_path), Some(old_text), Some(new_text)) =
        (path_opt, old_text_opt, new_text_opt)
    {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str = edit_text_block(&file_path, &old_text, &new_text);

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "edit_block").await
    }
}

async fn execute_command_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    let args = &tool_call_action.arguments;
    let command_opt = args
        .get("command")
        .and_then(|v| v.as_str())
        .map(String::from);
    let cwd_opt = args.get("cwd").and_then(|v| v.as_str()).map(String::from);
    let timeout_seconds = args
        .get("timeout_seconds")
        .and_then(|v| v.as_f64())
        .unwrap_or(30.0);

    if let Some(command) = command_opt {
        display::print_formatted_message(ai_app_message_for_display)?;

        let tool_output_str =
            execute_shell_command(&command, cwd_opt.as_deref(), timeout_seconds).await;

        let truncated_output = display::truncate_tool_output(&tool_output_str);
        let tool_result_display_msg = AppMessage {
            sender: "Tool Execution".to_string(),
            parts: vec![MessageContent::Text(truncated_output.clone())],
        };
        display::print_formatted_message(&tool_result_display_msg)?;

        // Re-acquire lock to update history
        let mut app_locked_again = app_arc.lock().unwrap();
        app_locked_again.messages.push(tool_result_display_msg);
        app_locked_again
            .add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_output_str);
        Ok(true)
    } else {
        handle_malformed_tool_args(tool_call_action, app_arc, "execute_command").await
    }
}

async fn execute_unknown_tool(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    ai_app_message_for_display: &AppMessage,
) -> Result<bool, Box<dyn std::error::Error>> {
    // Handle unknown tools
    let tool_not_impl_text = format!(
        "Tool '{}' is not implemented in this version.",
        tool_call_action.tool_name
    );
    let tool_not_impl_msg = AppMessage {
        sender: "System".to_string(),
        parts: vec![MessageContent::Text(tool_not_impl_text.clone())],
    };
    let mut app_locked = app_arc.lock().unwrap();
    app_locked.messages.push(tool_not_impl_msg.clone());
    app_locked.add_tool_response_to_llm_history(&tool_call_action.tool_name, tool_not_impl_text);
    drop(app_locked); // Release lock
    display::print_formatted_message(ai_app_message_for_display)?; // Show the AI's request
    display::print_formatted_message(&tool_not_impl_msg)?; // Show the system message
    Ok(true) // Let LLM know about this
}

async fn execute_shell_command(command: &str, cwd: Option<&str>, timeout_seconds: f64) -> String {
    let timeout_duration = Duration::from_secs_f64(timeout_seconds);

    let mut cmd = if cfg!(target_os = "windows") {
        let mut cmd = TokioCommand::new("cmd");
        cmd.args(["/C", command]);
        cmd
    } else {
        let mut cmd = TokioCommand::new("sh");
        cmd.args(["-c", command]);
        cmd
    };

    if let Some(working_dir) = cwd {
        cmd.current_dir(working_dir);
    }

    match timeout(timeout_duration, cmd.output()).await {
        Ok(Ok(output)) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            let exit_code = output.status.code().unwrap_or(-1);

            if exit_code == 0 {
                if stdout.is_empty() && stderr.is_empty() {
                    format!("Command '{}' executed successfully (no output)", command)
                } else {
                    format!(
                        "Command '{}' executed successfully:\n{}{}",
                        command,
                        stdout,
                        if stderr.is_empty() {
                            String::new()
                        } else {
                            format!("STDERR:\n{}", stderr)
                        }
                    )
                }
            } else {
                format!(
                    "Command '{}' failed with exit code {}:\nSTDOUT:\n{}\nSTDERR:\n{}",
                    command, exit_code, stdout, stderr
                )
            }
        }
        Ok(Err(e)) => {
            format!("Failed to execute command '{}': {}", command, e)
        }
        Err(_) => {
            format!(
                "Command '{}' timed out after {:.1} seconds",
                command, timeout_seconds
            )
        }
    }
}

async fn handle_malformed_tool_args(
    tool_call_action: &llm_client::ToolCallAction,
    app_arc: Arc<Mutex<App>>,
    tool_name: &str,
) -> Result<bool, Box<dyn std::error::Error>> {
    let err_text = format!(
        "System: AI provided malformed arguments for {} tool.",
        tool_name
    );
    let mut app_locked = app_arc.lock().unwrap();
    app_locked.add_tool_response_to_llm_history(&tool_call_action.tool_name, err_text.clone());
    let err_display_msg = AppMessage {
        sender: "System".to_string(),
        parts: vec![MessageContent::Text(err_text)],
    };
    app_locked.messages.push(err_display_msg.clone());
    drop(app_locked);
    display::print_formatted_message(&err_display_msg)?;
    Ok(true)
}
