use std::{fs, path::Path};

// Helper function to search files by name
pub fn search_files_by_name(pattern: &str, search_path: &str) -> String {
    let mut results = Vec::new();
    let pattern_lower = pattern.to_lowercase();

    fn search_recursive(dir: &Path, pattern: &str, results: &mut Vec<String>) {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                let name = path
                    .file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("")
                    .to_lowercase();

                if name.contains(pattern) {
                    results.push(path.to_string_lossy().to_string());
                }

                if path.is_dir() {
                    search_recursive(&path, pattern, results);
                }
            }
        }
    }

    search_recursive(Path::new(search_path), &pattern_lower, &mut results);

    if results.is_empty() {
        format!(
            "No files found matching pattern '{}' in '{}'",
            pattern, search_path
        )
    } else {
        results.sort();
        format!(
            "Found {} files matching pattern '{}':\n{}",
            results.len(),
            pattern,
            results.join("\n")
        )
    }
}

// Helper function to search code content
pub fn search_code_content(pattern: &str, search_path: &str, file_pattern: Option<&str>) -> String {
    let mut results = Vec::new();

    fn search_in_file(file_path: &Path, pattern: &str, results: &mut Vec<String>) {
        if let Ok(content) = fs::read_to_string(file_path) {
            for (line_num, line) in content.lines().enumerate() {
                if line.to_lowercase().contains(&pattern.to_lowercase()) {
                    results.push(format!(
                        "{}:{}: {}",
                        file_path.display(),
                        line_num + 1,
                        line.trim()
                    ));
                }
            }
        }
    }

    fn search_recursive(
        dir: &Path,
        pattern: &str,
        file_pattern: Option<&str>,
        results: &mut Vec<String>,
    ) {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();

                if path.is_file() {
                    let should_search = if let Some(fp) = file_pattern {
                        if let Some(ext) = fp.strip_prefix("*.") {
                            path.extension()
                                .and_then(|e| e.to_str())
                                .map(|e| e == ext)
                                .unwrap_or(false)
                        } else {
                            path.file_name()
                                .and_then(|n| n.to_str())
                                .map(|n| n.contains(fp))
                                .unwrap_or(false)
                        }
                    } else {
                        true
                    };

                    if should_search {
                        search_in_file(&path, pattern, results);
                    }
                } else if path.is_dir() {
                    search_recursive(&path, pattern, file_pattern, results);
                }
            }
        }
    }

    search_recursive(Path::new(search_path), pattern, file_pattern, &mut results);

    if results.is_empty() {
        format!(
            "No matches found for pattern '{}' in '{}'",
            pattern, search_path
        )
    } else {
        format!(
            "Found {} matches for pattern '{}':\n{}",
            results.len(),
            pattern,
            results.join("\n")
        )
    }
}
