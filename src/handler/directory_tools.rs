use std::{fs, path::Path, time::SystemTime};

// Helper function to list directory contents with detailed info
pub fn list_directory_detailed(dir_path: &str) -> String {
    match fs::read_dir(dir_path) {
        Ok(entries) => {
            let mut result = format!("Detailed directory listing for '{}':\n", dir_path);
            let mut items = Vec::new();

            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let path = entry.path();
                        let name = path.file_name().unwrap_or_default().to_string_lossy();

                        if let Ok(metadata) = entry.metadata() {
                            let item_type = if metadata.is_dir() { "DIR " } else { "FILE" };
                            let size = if metadata.is_file() {
                                format!("{:>10}", metadata.len())
                            } else {
                                "         -".to_string()
                            };

                            let modified = metadata
                                .modified()
                                .ok()
                                .and_then(|time| time.duration_since(SystemTime::UNIX_EPOCH).ok())
                                .map(|duration| {
                                    let secs = duration.as_secs();
                                    chrono::DateTime::from_timestamp(secs as i64, 0)
                                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                                        .unwrap_or_else(|| "Unknown".to_string())
                                })
                                .unwrap_or_else(|| "Unknown".to_string());

                            items.push(format!("{} {} {} {}", item_type, size, modified, name));
                        } else {
                            items.push(format!("ERROR reading metadata for {}", name));
                        }
                    }
                    Err(e) => {
                        items.push(format!("ERROR: {}", e));
                    }
                }
            }

            items.sort();
            for item in items {
                result.push_str(&format!("{}\n", item));
            }
            result
        }
        Err(e) => {
            format!("Failed to list directory '{}': {}", dir_path, e)
        }
    }
}

// Helper function to list directory contents (recursive with details)
pub fn list_directory_detailed_recursive(dir_path: &str) -> String {
    fn walk_dir(path: &Path, prefix: &str, result: &mut String) {
        match fs::read_dir(path) {
            Ok(entries) => {
                let mut items: Vec<_> = entries.collect();
                items.sort_by_key(|entry| entry.as_ref().map(|e| e.path()).unwrap_or_default());

                for entry in items {
                    match entry {
                        Ok(entry) => {
                            let entry_path = entry.path();
                            let name = entry_path.file_name().unwrap_or_default().to_string_lossy();

                            if let Ok(metadata) = entry.metadata() {
                                let item_type = if metadata.is_dir() { "DIR " } else { "FILE" };
                                let size = if metadata.is_file() {
                                    format!("{:>8}", metadata.len())
                                } else {
                                    "       -".to_string()
                                };

                                result.push_str(&format!(
                                    "{}{} {} {}\n",
                                    prefix, item_type, size, name
                                ));

                                if metadata.is_dir() {
                                    let new_prefix = format!("{}  ", prefix);
                                    walk_dir(&entry_path, &new_prefix, result);
                                }
                            } else {
                                result.push_str(&format!(
                                    "{}ERROR reading metadata for {}\n",
                                    prefix, name
                                ));
                            }
                        }
                        Err(e) => {
                            result.push_str(&format!("{}ERROR: {}\n", prefix, e));
                        }
                    }
                }
            }
            Err(e) => {
                result.push_str(&format!("{}ERROR reading directory: {}\n", prefix, e));
            }
        }
    }

    let mut result = format!("Recursive detailed directory listing for '{}':\n", dir_path);
    walk_dir(Path::new(dir_path), "", &mut result);
    result
}
