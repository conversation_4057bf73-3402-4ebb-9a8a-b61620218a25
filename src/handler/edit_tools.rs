use std::fs;

// Helper function to edit text blocks
pub fn edit_text_block(file_path: &str, old_text: &str, new_text: &str) -> String {
    match fs::read_to_string(file_path) {
        Ok(content) => {
            if content.contains(old_text) {
                let new_content = content.replace(old_text, new_text);
                match fs::write(file_path, &new_content) {
                    Ok(_) => {
                        let old_lines = old_text.lines().count();
                        let new_lines = new_text.lines().count();
                        format!(
                            "Successfully edited '{}'\nReplaced {} lines with {} lines\nOld text: {}\nNew text: {}",
                            file_path,
                            old_lines,
                            new_lines,
                            if old_text.len() > 100 { format!("{}...", &old_text[..100]) } else { old_text.to_string() },
                            if new_text.len() > 100 { format!("{}...", &new_text[..100]) } else { new_text.to_string() }
                        )
                    }
                    Err(e) => format!("Failed to write changes to '{}': {}", file_path, e),
                }
            } else {
                format!(
                    "Text block not found in file '{}'. No changes made.",
                    file_path
                )
            }
        }
        Err(e) => {
            format!("Failed to read file '{}': {}", file_path, e)
        }
    }
}
