use std::fs;
use std::io::Write;

// Helper function to read file with pagination support
pub fn read_file_with_pagination(file_path: &str, offset: i64, length: Option<i64>) -> String {
    match fs::read_to_string(file_path) {
        Ok(content) => {
            let lines: Vec<&str> = content.lines().collect();
            let total_lines = lines.len() as i64;

            if total_lines == 0 {
                return format!("File '{}' is empty.", file_path);
            }

            // Calculate start index
            let start_idx = if offset >= 0 {
                offset.min(total_lines - 1) as usize
            } else {
                (total_lines + offset).max(0) as usize
            };

            // Calculate end index
            let end_idx = if let Some(len) = length {
                (start_idx as i64 + len).min(total_lines) as usize
            } else {
                total_lines as usize
            };

            let selected_lines = &lines[start_idx..end_idx];
            let result_content = selected_lines.join("\n");

            format!(
                "File '{}' (lines {}-{} of {}):\n{}",
                file_path,
                start_idx + 1,
                end_idx,
                total_lines,
                result_content
            )
        }
        Err(e) => {
            format!("Failed to read file '{}': {}", file_path, e)
        }
    }
}

// Helper function to write file with mode support
pub fn write_file_with_mode(file_path: &str, content: &str, mode: &str) -> String {
    match mode {
        "append" => {
            match fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(file_path)
            {
                Ok(mut file) => match file.write_all(content.as_bytes()) {
                    Ok(_) => format!(
                        "Content appended to file '{}'. {} bytes added.",
                        file_path,
                        content.len()
                    ),
                    Err(e) => format!("Failed to append to file '{}': {}", file_path, e),
                },
                Err(e) => format!("Failed to open file '{}' for appending: {}", file_path, e),
            }
        }
        _ => {
            // "rewrite" or default
            match fs::write(file_path, content) {
                Ok(_) => format!(
                    "File '{}' written successfully. {} bytes written.",
                    file_path,
                    content.len()
                ),
                Err(e) => format!("Failed to write file '{}': {}", file_path, e),
            }
        }
    }
}
